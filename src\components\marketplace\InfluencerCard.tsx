'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { CheckCircle, Star, ExternalLink } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import type { InfluencerSearchResult } from '@/lib/marketplace';

interface InfluencerCardProps {
  influencer: InfluencerSearchResult;
}

export function InfluencerCard({ influencer }: InfluencerCardProps) {
  const formatPrice = (price: number) => {
    return `${price.toLocaleString()} KM`;
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(
          <Star key={i} className="w-3.5 h-3.5 fill-amber-400 text-amber-400" />
        );
      } else if (i === fullStars && hasHalfStar) {
        stars.push(
          <div key={i} className="relative w-3.5 h-3.5">
            <Star className="w-3.5 h-3.5 text-gray-300 absolute" />
            <Star className="w-3.5 h-3.5 fill-amber-400 text-amber-400 absolute overflow-hidden" style={{ clipPath: 'inset(0 50% 0 0)' }} />
          </div>
        );
      } else {
        stars.push(
          <Star key={i} className="w-3.5 h-3.5 text-gray-300" />
        );
      }
    }

    return stars;
  };

  const formatFollowers = (count: number) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  // Rezervisano mjesto - uvijek 3 platforme
  const displayPlatforms = [
    ...influencer.platforms.slice(0, 3),
    ...Array(Math.max(0, 3 - influencer.platforms.length)).fill(null)
  ];

  // Rezervisano mjesto - uvijek 2 paketa
  const displayPricing = [
    ...influencer.pricing.slice(0, 2),
    ...Array(Math.max(0, 2 - influencer.pricing.length)).fill(null)
  ];

  // Glavna kategorija za prikaz
  const mainCategory = influencer.categories?.[0]?.name || 'Influencer';

  return (
    <Link href={`/influencer/${influencer.username}`}>
      <div className="p-1 bg-gradient-to-br from-pink-500 via-purple-500 to-orange-400 rounded-xl group hover:shadow-lg transition-all duration-200">
        <Card className="w-full overflow-hidden bg-white h-full py-0">
          <CardContent className="p-0">
            {/* Image section */}
            <div className="relative w-full aspect-square overflow-hidden bg-gray-100">
              <Image
                src={influencer.avatar_url || "/placeholder.svg"}
                alt={`${influencer.full_name || influencer.username} profile`}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />

              {/* Verified badge - top left */}
              {influencer.is_verified && (
                <div className="absolute top-3 left-3 z-20">
                  <Badge
                    variant="secondary"
                    className="bg-gradient-to-r from-pink-500 to-purple-500 text-white border-0 px-2 py-1 shadow-lg"
                  >
                    <CheckCircle className="w-3 h-3 mr-1 fill-current" />
                    Verified
                  </Badge>
                </div>
              )}

              {/* Star rating - top right */}
              <div className="absolute top-3 right-3 z-20">
                <div className="bg-white/95 backdrop-blur-sm rounded-full px-3 py-1.5 flex items-center gap-1.5 shadow-md border border-white/20">
                  <div className="flex items-center gap-0.5">
                    {renderStars(influencer.average_rating || 0)}
                  </div>
                  <span className="text-gray-900 text-sm font-medium">{(influencer.average_rating || 0).toFixed(1)}</span>
                </div>
              </div>

              {/* Username - bottom left */}
              <div className="absolute bottom-3 left-3 z-20">
                <div className="bg-black/60 backdrop-blur-sm rounded-lg px-3 py-2">
                  <h3 className="text-white font-semibold">@{influencer.username}</h3>
                </div>
              </div>
            </div>

            {/* Info section */}
            <div className="p-3 space-y-2">
              {/* Location */}
              {influencer.location && (
                <div className="text-left">
                  <p className="text-xs text-gray-500">{influencer.location}</p>
                </div>
              )}

              {/* Bio */}
              {influencer.bio && (
                <p className="text-xs text-gray-600 line-clamp-2">{influencer.bio}</p>
              )}

              {/* Social handles with follower counts */}
              <div className="space-y-1.5">
                {displayPlatforms.slice(0, 2).map((platform, index) => (
                  platform ? (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <div className="p-1 bg-gradient-to-br from-pink-500 via-purple-500 to-orange-400 rounded-full">
                          <span className="text-white text-xs">{platform.platform_icon}</span>
                        </div>
                        <span className="truncate max-w-[100px]">{platform.handle}</span>
                      </div>
                      <span className="text-xs font-medium text-gray-700">
                        {formatFollowers(platform.followers_count)}
                      </span>
                    </div>
                  ) : (
                    <div key={index} className="flex items-center justify-between opacity-30">
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <div className="p-1 bg-gray-200 rounded-full">
                          <span className="text-gray-400 text-xs">📱</span>
                        </div>
                        <span>-</span>
                      </div>
                      <span className="text-xs text-gray-400">-</span>
                    </div>
                  )
                ))}
              </div>

              {/* Separator */}
              <hr className="border-gray-200" />

              {/* Packages */}
              <div className="space-y-1.5">
                <h4 className="text-xs font-semibold text-gray-700">Paketi</h4>
                {displayPricing.slice(0, 2).map((pkg, index) => (
                  pkg ? (
                    <div key={index} className="flex justify-between items-center">
                      <span className="text-xs text-gray-600 truncate max-w-[120px]">
                        {pkg.content_type_name}
                      </span>
                      <span className="text-xs font-medium text-gradient bg-gradient-to-r from-pink-500 to-purple-500 bg-clip-text text-transparent">
                        {formatPrice(pkg.price)}
                      </span>
                    </div>
                  ) : (
                    <div key={index} className="flex justify-between items-center opacity-30">
                      <span className="text-xs text-gray-400">-</span>
                      <span className="text-xs text-gray-400">-</span>
                    </div>
                  )
                ))}
              </div>

              {/* View Packages Button */}
              <div className="pt-1.5">
                <Button className="w-full bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600 text-white shadow-lg hover:shadow-xl transition-all duration-300 group h-9 rounded-xl text-sm">
                  <span className="font-medium">Pogledaj pakete</span>
                  <ExternalLink className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-200" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </Link>
  );
}
