import { ProfileCard } from "./components/ProfileCard";

export default function App() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-indigo-50 to-purple-100 p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent mb-4">
            Influencer Marketplace
          </h1>
          <p className="text-gray-600 text-lg">Discover and hire top content creators for your brand</p>
        </div>

        {/* Profile Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 place-items-center">
          <ProfileCard
            username="emmarodriguez"
            profileImage="https://images.unsplash.com/photo-1678730939174-bf600a5e0af2?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHx5b3VuZyUyMHdvbWFuJTIwaW5mbHVlbmNlciUyMHBvcnRyYWl0fGVufDF8fHx8MTc1NDY0MjA4NHww&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral"
            isVerified={true}
            rating={4.8}
            instagramHandle="emmarodriguez"
            instagramFollowers="125K"
            tiktokHandle="emmarodriguez"
            tiktokFollowers="89K"
            youtubeHandle="emmarodriguezofficial"
            youtubeFollowers="45K"
            price="From 250€"
          />
          
          <ProfileCard
            username="alexchen1"
            profileImage="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=1080"
            isVerified={true}
            rating={4.6}
            instagramHandle="alexchen1"
            instagramFollowers="98K"
            tiktokHandle="alexchen1"
            tiktokFollowers="156K"
            youtubeHandle="alexchen1"
            youtubeFollowers="67K"
            price="From 180€"
          />
          
          <ProfileCard
            username="sophiamartinez"
            profileImage="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=1080"
            isVerified={false}
            rating={4.2}
            instagramHandle="sophiamartinez"
            instagramFollowers="78K"
            tiktokHandle="sophiamartinez"
            tiktokFollowers="112K"
            youtubeHandle="sophiamartinez"
            youtubeFollowers="34K"
            price="From 150€"
          />

          <ProfileCard
            username="mikejohnson23"
            profileImage="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=1080"
            isVerified={true}
            rating={4.9}
            instagramHandle="mikejohnson23"
            instagramFollowers="200K"
            tiktokHandle="mikejohnson23"
            tiktokFollowers="340K"
            youtubeHandle="mikejohnson23"
            youtubeFollowers="120K"
            price="From 320€"
          />
        </div>
      </div>
    </div>
  );
}