import { <PERSON>, CardContent, CardHeader } from "./ui/card";
import { Badge } from "./ui/badge";
import { But<PERSON> } from "./ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";
import { Separator } from "./ui/separator";
import { ImageWithFallback } from "./figma/ImageWithFallback";
import { Check, Star, Instagram, Youtube, ExternalLink } from "lucide-react";

interface ProfileCardProps {
  username: string;
  profileImage: string;
  isVerified?: boolean;
  rating: number;
  instagramHandle: string;
  instagramFollowers: string;
  tiktokHandle: string;
  tiktokFollowers: string;
  youtubeHandle: string;
  youtubeFollowers: string;
  price: string;
  specialization?: string;
}

export function ProfileCard({
  username,
  profileImage,
  isVerified = true,
  rating,
  instagramHandle,
  instagramFollowers,
  tiktokHandle,
  tiktokFollowers,
  youtubeHandle,
  youtubeFollowers,
  price,
  specialization = "Content Creator"
}: ProfileCardProps) {
  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    
    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(
          <Star key={i} className="w-3.5 h-3.5 fill-amber-400 text-amber-400" />
        );
      } else if (i === fullStars && hasHalfStar) {
        stars.push(
          <div key={i} className="relative w-3.5 h-3.5">
            <Star className="w-3.5 h-3.5 text-gray-300 absolute" />
            <Star className="w-3.5 h-3.5 fill-amber-400 text-amber-400 absolute overflow-hidden" style={{ clipPath: 'inset(0 50% 0 0)' }} />
          </div>
        );
      } else {
        stars.push(
          <Star key={i} className="w-3.5 h-3.5 text-gray-300" />
        );
      }
    }
    
    return stars;
  };

  const TikTokIcon = () => (
    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" className="text-gray-700">
      <path d="M19.59 6.69a4.83 4.83 0 01-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 01-5.2 1.74 2.89 2.89 0 012.31-4.64 2.93 2.93 0 01.88.13V9.4a6.84 6.84 0 00-.88-.05A6.33 6.33 0 005 20.1a6.34 6.34 0 0010.86-4.43v-7a8.16 8.16 0 004.77 1.52v-3.4a4.85 4.85 0 01-1-.10z"/>
    </svg>
  );

  return (
    <Card className="w-80 overflow-hidden bg-white shadow-lg hover:shadow-2xl transition-all duration-300 border-0 ring-1 ring-gray-200 hover:ring-purple-200 hover:scale-[1.02]">
      {/* Profile Picture Section - Full width 1:1 ratio */}
      <CardHeader className="p-0 relative">
        <div className="relative w-full aspect-square bg-gradient-to-br from-blue-50 to-purple-50">
          <ImageWithFallback
            src={profileImage}
            alt={`@${username}`}
            className="w-full h-full object-cover"
          />
          
          {/* Verified Badge - Top Left */}
          {isVerified && (
            <Badge className="absolute top-3 left-3 bg-emerald-500 hover:bg-emerald-600 text-white px-2 py-1 flex items-center gap-1 shadow-md">
              <Check className="w-3 h-3" />
              <span className="text-xs font-medium">Verified</span>
            </Badge>
          )}
          
          {/* Rating - Top Right */}
          <div className="absolute top-3 right-3 bg-white/95 backdrop-blur-sm rounded-full px-3 py-1.5 flex items-center gap-1.5 shadow-md border border-white/20">
            <div className="flex items-center gap-0.5">
              {renderStars(rating)}
            </div>
            <span className="text-gray-900 text-sm font-medium">{rating}</span>
          </div>

          {/* Username - Bottom Left */}
          <div className="absolute bottom-3 left-3 bg-black/60 backdrop-blur-sm rounded-lg px-3 py-2">
            <h3 className="text-white font-semibold">@{username}</h3>
          </div>
        </div>
      </CardHeader>

      <CardContent className="px-6 pb-6 pt-4 space-y-4">

        {/* Social Media Stats */}
        <div className="space-y-3">
          {/* Instagram */}
          <div className="flex items-center justify-between p-3 rounded-xl bg-gradient-to-r from-pink-500/10 to-purple-500/10 border border-pink-200/50 hover:border-pink-300/70 transition-all duration-200 group">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-white shadow-sm group-hover:shadow-md transition-shadow">
                <Instagram className="w-4 h-4 text-pink-600" />
              </div>
              <div>
                <span className="text-sm font-medium text-gray-900">Instagram</span>
                <p className="text-xs text-gray-500">@{instagramHandle}</p>
              </div>
            </div>
            <div className="text-right">
              <span className="text-lg font-bold text-gray-900">{instagramFollowers}</span>
              <p className="text-xs text-gray-500 uppercase tracking-wide">followers</p>
            </div>
          </div>

          {/* TikTok */}
          <div className="flex items-center justify-between p-3 rounded-xl bg-gradient-to-r from-gray-500/10 to-slate-500/10 border border-gray-200/50 hover:border-gray-300/70 transition-all duration-200 group">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-white shadow-sm group-hover:shadow-md transition-shadow">
                <TikTokIcon />
              </div>
              <div>
                <span className="text-sm font-medium text-gray-900">TikTok</span>
                <p className="text-xs text-gray-500">@{tiktokHandle}</p>
              </div>
            </div>
            <div className="text-right">
              <span className="text-lg font-bold text-gray-900">{tiktokFollowers}</span>
              <p className="text-xs text-gray-500 uppercase tracking-wide">followers</p>
            </div>
          </div>

          {/* YouTube */}
          <div className="flex items-center justify-between p-3 rounded-xl bg-gradient-to-r from-red-500/10 to-orange-500/10 border border-red-200/50 hover:border-red-300/70 transition-all duration-200 group">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-white shadow-sm group-hover:shadow-md transition-shadow">
                <Youtube className="w-4 h-4 text-red-600" />
              </div>
              <div>
                <span className="text-sm font-medium text-gray-900">YouTube</span>
                <p className="text-xs text-gray-500">@{youtubeHandle}</p>
              </div>
            </div>
            <div className="text-right">
              <span className="text-lg font-bold text-gray-900">{youtubeFollowers}</span>
              <p className="text-xs text-gray-500 uppercase tracking-wide">subscribers</p>
            </div>
          </div>
        </div>

        {/* Pricing Section */}
        <div className="space-y-4 pt-2">
          <div className="flex items-center justify-between p-4 rounded-xl bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200/50">
            <div>
              <p className="text-xs text-gray-600 font-medium uppercase tracking-wide">Starting from</p>
              <span className="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">{price}</span>
            </div>
            <Badge className="bg-emerald-100 text-emerald-800 hover:bg-emerald-200 border-emerald-200">
              Available
            </Badge>
          </div>
          
          <Button className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 group h-11 rounded-xl">
            <span className="font-medium">View Packages</span>
            <ExternalLink className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-200" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}